/**
 * 企业权限管理面板组件
 * 提供团队管理、角色权限、项目访问控制、审计日志等功能界面
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  Row,
  Col,
  List,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Table,
  Tooltip,
  Badge,
  Progress,
  Alert,
  Divider,
  Popover,
  Switch,
  DatePicker,
  Statistic,
  Timeline,
  Transfer,
  Tree,
  Descriptions
} from 'antd';
import {
  TeamOutlined,
  UserOutlined,
  SafetyOutlined,
  AuditOutlined,
  SettingOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  KeyOutlined,
  ShieldOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  CrownOutlined,
  SecurityScanOutlined,
  FileProtectOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import EnterprisePermissionService, {
  User,
  Team,
  UserRole,
  PermissionType,
  AuditLog,
  AuditActionType
} from '../../services/EnterprisePermissionService';
import './EnterprisePermissionPanel.less';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface EnterprisePermissionPanelProps {
  visible: boolean;
  onClose: () => void;
}

const EnterprisePermissionPanel: React.FC<EnterprisePermissionPanelProps> = ({
  visible,
  onClose
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('users');
  const [users, setUsers] = useState<User[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [permissionStats, setPermissionStats] = useState<any>({});
  const [userModalVisible, setUserModalVisible] = useState(false);
  const [teamModalVisible, setTeamModalVisible] = useState(false);

  const permissionService = EnterprisePermissionService.getInstance();

  useEffect(() => {
    if (visible) {
      loadData();
      setupEventListeners();
    }

    return () => {
      cleanupEventListeners();
    };
  }, [visible]);

  const setupEventListeners = () => {
    permissionService.on('userCreated', handleUserCreated);
    permissionService.on('userUpdated', handleUserUpdated);
    permissionService.on('teamCreated', handleTeamCreated);
    permissionService.on('auditLogCreated', handleAuditLogCreated);
  };

  const cleanupEventListeners = () => {
    permissionService.off('userCreated', handleUserCreated);
    permissionService.off('userUpdated', handleUserUpdated);
    permissionService.off('teamCreated', handleTeamCreated);
    permissionService.off('auditLogCreated', handleAuditLogCreated);
  };

  const loadData = () => {
    setUsers(permissionService.getAllUsers());
    setTeams(permissionService.getAllTeams());
    setAuditLogs(permissionService.getAuditLogs({ limit: 100 }));
    setPermissionStats(permissionService.getPermissionStats());
  };

  const handleUserCreated = (user: User) => {
    setUsers(prev => [...prev, user]);
    setPermissionStats(permissionService.getPermissionStats());
  };

  const handleUserUpdated = (user: User) => {
    setUsers(prev => prev.map(u => u.id === user.id ? user : u));
  };

  const handleTeamCreated = (team: Team) => {
    setTeams(prev => [...prev, team]);
    setPermissionStats(permissionService.getPermissionStats());
  };

  const handleAuditLogCreated = (log: AuditLog) => {
    setAuditLogs(prev => [log, ...prev.slice(0, 99)]);
  };

  const handleCreateUser = async (values: any) => {
    try {
      await permissionService.createUser({
        username: values.username,
        email: values.email,
        displayName: values.displayName,
        role: values.role,
        teamId: values.teamId
      });
      setUserModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('Failed to create user:', error);
    }
  };

  const handleCreateTeam = async (values: any) => {
    try {
      await permissionService.createTeam({
        name: values.name,
        description: values.description,
        maxMembers: values.maxMembers
      });
      setTeamModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('Failed to create team:', error);
    }
  };

  const handleUpdateUserRole = async (userId: string, role: UserRole) => {
    try {
      await permissionService.updateUser(userId, { role });
    } catch (error) {
      console.error('Failed to update user role:', error);
    }
  };

  const getRoleColor = (role: UserRole): string => {
    switch (role) {
      case UserRole.SUPER_ADMIN:
        return 'red';
      case UserRole.ADMIN:
        return 'orange';
      case UserRole.MANAGER:
        return 'blue';
      case UserRole.EDITOR:
        return 'green';
      case UserRole.VIEWER:
        return 'default';
      case UserRole.GUEST:
        return 'gray';
      default:
        return 'default';
    }
  };

  const getRiskLevelColor = (level: string): string => {
    switch (level) {
      case 'critical':
        return 'red';
      case 'high':
        return 'orange';
      case 'medium':
        return 'yellow';
      case 'low':
        return 'green';
      default:
        return 'default';
    }
  };

  // 渲染用户管理
  const renderUserManagement = () => {
    const columns = [
      {
        title: t('permission.user'),
        dataIndex: 'displayName',
        key: 'displayName',
        render: (name: string, user: User) => (
          <Space>
            <UserOutlined />
            <div>
              <Text strong>{name}</Text>
              <br />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {user.email}
              </Text>
            </div>
          </Space>
        )
      },
      {
        title: t('permission.role'),
        dataIndex: 'role',
        key: 'role',
        render: (role: UserRole) => (
          <Tag color={getRoleColor(role)}>{role.toUpperCase()}</Tag>
        )
      },
      {
        title: t('permission.teams'),
        dataIndex: 'teams',
        key: 'teams',
        render: (teamIds: string[]) => (
          <div>
            {teamIds.slice(0, 2).map(teamId => {
              const team = teams.find(t => t.id === teamId);
              return team ? (
                <Tag key={teamId} style={{ fontSize: '12px' }}>{team.name}</Tag>
              ) : null;
            })}
            {teamIds.length > 2 && (
              <Tag style={{ fontSize: '12px' }}>+{teamIds.length - 2}</Tag>
            )}
          </div>
        )
      },
      {
        title: t('permission.status'),
        dataIndex: 'isActive',
        key: 'isActive',
        render: (isActive: boolean) => (
          <Badge
            status={isActive ? 'success' : 'error'}
            text={isActive ? t('permission.active') : t('permission.inactive')}
          />
        )
      },
      {
        title: t('permission.lastLogin'),
        dataIndex: 'lastLoginAt',
        key: 'lastLoginAt',
        render: (timestamp?: number) => (
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {timestamp ? new Date(timestamp).toLocaleString() : t('permission.never')}
          </Text>
        )
      },
      {
        title: t('permission.actions'),
        key: 'actions',
        render: (_, user: User) => (
          <Space>
            <Tooltip title={t('permission.viewDetails')}>
              <Button
                type="text"
                size="small"
                icon={<EyeOutlined />}
                onClick={() => console.log('View user details:', user)}
              />
            </Tooltip>
            <Tooltip title={t('permission.editPermissions')}>
              <Button
                type="text"
                size="small"
                icon={<KeyOutlined />}
                onClick={() => console.log('Edit user permissions:', user)}
              />
            </Tooltip>
            <Tooltip title={t('permission.editUser')}>
              <Button
                type="text"
                size="small"
                icon={<EditOutlined />}
              />
            </Tooltip>
            <Tooltip title={t('permission.deleteUser')}>
              <Button
                type="text"
                size="small"
                icon={<DeleteOutlined />}
                danger
              />
            </Tooltip>
          </Space>
        )
      }
    ];

    return (
      <div className="user-management">
        <div className="section-header">
          <Space>
            <Title level={5}>{t('permission.userManagement')}</Title>
            <Badge count={users.length} showZero />
          </Space>
          <Button type="primary" onClick={() => setUserModalVisible(true)}>
            <PlusOutlined />
            {t('permission.addUser')}
          </Button>
        </div>

        <Table
          dataSource={users}
          columns={columns}
          rowKey="id"
          size="small"
          pagination={{ pageSize: 10 }}
        />
      </div>
    );
  };

  // 渲染团队管理
  const renderTeamManagement = () => (
    <div className="team-management">
      <div className="section-header">
        <Space>
          <Title level={5}>{t('permission.teamManagement')}</Title>
          <Badge count={teams.length} showZero />
        </Space>
        <Button type="primary" onClick={() => setTeamModalVisible(true)}>
          <PlusOutlined />
          {t('permission.createTeam')}
        </Button>
      </div>

      <Row gutter={[16, 16]}>
        {teams.map(team => (
          <Col key={team.id} span={8}>
            <Card
              size="small"
              title={
                <Space>
                  <TeamOutlined />
                  <Text strong>{team.name}</Text>
                  {!team.isActive && (
                    <Tag color="red" style={{ fontSize: '12px' }}>Inactive</Tag>
                  )}
                </Space>
              }
              extra={
                <Button
                  type="text"
                  size="small"
                  onClick={() => console.log('View team details:', team)}
                >
                  <EyeOutlined />
                </Button>
              }
            >
              <div className="team-info">
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {team.description}
                </Text>
                <Divider />
                <Row gutter={[8, 8]}>
                  <Col span={12}>
                    <Statistic
                      title={t('permission.members')}
                      value={team.stats.memberCount}
                      prefix={<UserOutlined />}
                      valueStyle={{ fontSize: '14px' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title={t('permission.projects')}
                      value={team.stats.projectCount}
                      prefix={<FileProtectOutlined />}
                      valueStyle={{ fontSize: '14px' }}
                    />
                  </Col>
                </Row>
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>
                    Storage: {(team.stats.storageUsed / 1024 / 1024).toFixed(1)} MB / 
                    {(team.stats.storageLimit / 1024 / 1024 / 1024).toFixed(0)} GB
                  </Text>
                  <Progress
                    percent={(team.stats.storageUsed / team.stats.storageLimit) * 100}
                    size="small"
                    style={{ marginTop: 4 }}
                  />
                </div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );

  // 渲染审计日志
  const renderAuditLogs = () => {
    const columns = [
      {
        title: t('permission.timestamp'),
        dataIndex: 'timestamp',
        key: 'timestamp',
        render: (timestamp: number) => (
          <Text style={{ fontSize: '12px' }}>
            {new Date(timestamp).toLocaleString()}
          </Text>
        )
      },
      {
        title: t('permission.action'),
        dataIndex: 'actionType',
        key: 'actionType',
        render: (actionType: AuditActionType) => (
          <Tag style={{ fontSize: '12px' }}>{actionType}</Tag>
        )
      },
      {
        title: t('permission.user'),
        dataIndex: 'userId',
        key: 'userId',
        render: (userId: string) => {
          const user = users.find(u => u.id === userId);
          return user ? user.displayName : userId;
        }
      },
      {
        title: t('permission.target'),
        key: 'target',
        render: (_, log: AuditLog) => (
          <Text style={{ fontSize: '12px' }}>
            {log.targetType}: {log.targetId}
          </Text>
        )
      },
      {
        title: t('permission.riskLevel'),
        dataIndex: 'riskLevel',
        key: 'riskLevel',
        render: (level: string) => (
          <Tag color={getRiskLevelColor(level)} style={{ fontSize: '12px' }}>
            {level.toUpperCase()}
          </Tag>
        )
      },
      {
        title: t('permission.result'),
        dataIndex: 'result',
        key: 'result',
        render: (result: string) => (
          <Badge
            status={result === 'success' ? 'success' : 'error'}
            text={result}
          />
        )
      }
    ];

    return (
      <div className="audit-logs">
        <div className="section-header">
          <Space>
            <Title level={5}>{t('permission.auditLogs')}</Title>
            <Badge count={auditLogs.length} showZero />
          </Space>
          <Space>
            <RangePicker size="small" />
            <Select
              size="small"
              placeholder={t('permission.filterByAction')}
              style={{ width: 150 }}
              allowClear
            >
              {Object.values(AuditActionType).map(action => (
                <Option key={action} value={action}>{action}</Option>
              ))}
            </Select>
          </Space>
        </div>

        <Table
          dataSource={auditLogs}
          columns={columns}
          rowKey="id"
          size="small"
          pagination={{ pageSize: 20 }}
        />
      </div>
    );
  };

  // 渲染权限统计
  const renderPermissionStats = () => (
    <div className="permission-stats">
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Statistic
            title={t('permission.totalUsers')}
            value={permissionStats.totalUsers || 0}
            prefix={<UserOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title={t('permission.activeUsers')}
            value={permissionStats.activeUsers || 0}
            prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title={t('permission.totalTeams')}
            value={permissionStats.totalTeams || 0}
            prefix={<TeamOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title={t('permission.recentHighRiskActions')}
            value={permissionStats.recentHighRiskActions || 0}
            prefix={<WarningOutlined style={{ color: '#ff4d4f' }} />}
          />
        </Col>
      </Row>

      <Divider />

      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card size="small" title={t('permission.usersByRole')}>
            {Object.entries(permissionStats.usersByRole || {}).map(([role, count]) => (
              <div key={role} style={{ marginBottom: 8 }}>
                <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                  <Tag color={getRoleColor(role as UserRole)}>{role.toUpperCase()}</Tag>
                  <Text>{count as number}</Text>
                </Space>
              </div>
            ))}
          </Card>
        </Col>
        <Col span={12}>
          <Card size="small" title={t('permission.securityOverview')}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text type="secondary">{t('permission.twoFactorEnabled')}</Text>
                <Progress
                  percent={75}
                  size="small"
                  strokeColor="#52c41a"
                />
              </div>
              <div>
                <Text type="secondary">{t('permission.passwordCompliance')}</Text>
                <Progress
                  percent={85}
                  size="small"
                  strokeColor="#1890ff"
                />
              </div>
              <div>
                <Text type="secondary">{t('permission.accessCompliance')}</Text>
                <Progress
                  percent={92}
                  size="small"
                  strokeColor="#722ed1"
                />
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );

  return (
    <Modal
      title={
        <Space>
          <ShieldOutlined />
          {t('permission.enterprisePermission')}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1200}
      footer={[
        <Button key="close" onClick={onClose}>
          {t('common.close')}
        </Button>
      ]}
      className="enterprise-permission-panel"
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'users',
            label: t('permission.users'),
            children: renderUserManagement()
          },
          {
            key: 'teams',
            label: t('permission.teams'),
            children: renderTeamManagement()
          },
          {
            key: 'audit',
            label: t('permission.auditLogs'),
            children: renderAuditLogs()
          },
          {
            key: 'overview',
            label: t('permission.overview'),
            children: renderPermissionStats()
          }
        ]}
      />

      {/* 创建用户对话框 */}
      <Modal
        title={t('permission.createUser')}
        open={userModalVisible}
        onCancel={() => setUserModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} layout="vertical" onFinish={handleCreateUser}>
          <Form.Item
            name="username"
            label={t('permission.username')}
            rules={[{ required: true }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="email"
            label={t('permission.email')}
            rules={[{ required: true, type: 'email' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="displayName"
            label={t('permission.displayName')}
            rules={[{ required: true }]}
          >
            <Input />
          </Form.Item>
          <Form.Item name="role" label={t('permission.role')}>
            <Select defaultValue={UserRole.EDITOR}>
              {Object.values(UserRole).map(role => (
                <Option key={role} value={role}>{role.toUpperCase()}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="teamId" label={t('permission.team')}>
            <Select allowClear>
              {teams.map(team => (
                <Option key={team.id} value={team.id}>{team.name}</Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 创建团队对话框 */}
      <Modal
        title={t('permission.createTeam')}
        open={teamModalVisible}
        onCancel={() => setTeamModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} layout="vertical" onFinish={handleCreateTeam}>
          <Form.Item
            name="name"
            label={t('permission.teamName')}
            rules={[{ required: true }]}
          >
            <Input />
          </Form.Item>
          <Form.Item name="description" label={t('permission.description')}>
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item name="maxMembers" label={t('permission.maxMembers')}>
            <Input type="number" defaultValue={50} />
          </Form.Item>
        </Form>
      </Modal>
    </Modal>
  );
};

export default EnterprisePermissionPanel;
